import pandas as pd
import numpy as np
import re
from datetime import datetime
import openai
import os
from dotenv import load_dotenv

load_dotenv()

openai_api_key= os.getenv("API_KEY")

# Initialize clients and models
client = openai.OpenAI(api_key=openai_api_key)

import re

def parse_duration(duration_str: str) -> int:
    """
    Convert a duration string (e.g. 'PT1H30M', 'PT45M', or '1 hour 30 min')
    into total minutes as an integer. Returns 0 on parse failure.
    """
    try:
        # Normalize case
        s = duration_str.lower()

        # If it starts with 'pt', it might be ISO 8601 style (e.g., PT1H30M)
        if s.startswith("pt"):
            hours_match = re.search(r'(\d+)h', s)
            mins_match = re.search(r'(\d+)m', s)
            hours = int(hours_match.group(1)) if hours_match else 0
            mins = int(mins_match.group(1)) if mins_match else 0
            return hours * 60 + mins

        # Otherwise, try to parse textual forms like "1 hour 30 min"
        hours_match = re.search(r'(\d+)\s*hour', s)
        mins_match = re.search(r'(\d+)\s*min', s)
        hours = int(hours_match.group(1)) if hours_match else 0
        mins = int(mins_match.group(1)) if mins_match else 0
        return hours * 60 + mins
    except:
        return 0


def str_contains_any(text, search_list):
    """
    Returns True if 'text' contains ANY of the strings in 'search_list'.
    Case-insensitive partial match.
    """
    text_lower = str(text).lower()
    return any(s.lower() in text_lower for s in search_list)


def advanced_recipe_search(
    user_query: str,
    max_results: int = 10,
    df=None
) -> pd.DataFrame:
    """
    A thorough, flexible recipe search that parses user_query for multiple filters.
    """
    filters=generate_filters(client, user_query)


    print("Filters generated from user query are ", filters)

    # 2. Make a copy of the DataFrame to apply filters
    df_filtered = df.copy()

    # 3. Pre-process time columns (CookTime, PrepTime, TotalTime) into integer minutes
    for col in ["CookTime", "PrepTime", "TotalTime"]:
        if col in df_filtered.columns:
            df_filtered[col] = df_filtered[col].fillna("PT0M").astype(str)
            df_filtered[f"{col}Minutes"] = df_filtered[col].apply(parse_duration)

    # ============ Numeric Filters ============ #
    # Example: min_calories, max_calories, min_rating, etc.
    numeric_filters = [
        # (FilterKey, ColumnName)
        ("min_calories", "Calories"),
        ("max_calories", "Calories"),
        ("min_rating", "AggregatedRating"),
        ("max_rating", "AggregatedRating"),
        ("min_review_count", "ReviewCount"),
        ("max_review_count", "ReviewCount"),
        ("min_fat_content", "FatContent"),
        ("max_fat_content", "FatContent"),
        ("min_saturated_fat_content", "SaturatedFatContent"),
        ("max_saturated_fat_content", "SaturatedFatContent"),
        ("min_cholesterol_content", "CholesterolContent"),
        ("max_cholesterol_content", "CholesterolContent"),
        ("min_sodium_content", "SodiumContent"),
        ("max_sodium_content", "SodiumContent"),
        ("min_carbohydrate_content", "CarbohydrateContent"),
        ("max_carbohydrate_content", "CarbohydrateContent"),
        ("min_fiber_content", "FiberContent"),
        ("max_fiber_content", "FiberContent"),
        ("min_sugar_content", "SugarContent"),
        ("max_sugar_content", "SugarContent"),
        ("min_protein_content", "ProteinContent"),
        ("max_protein_content", "ProteinContent"),
    ]

    for filter_key, col_name in numeric_filters:
        if filter_key in filters and col_name in df_filtered.columns:
            val = filters[filter_key]
            # Convert to float if needed
            if pd.api.types.is_numeric_dtype(df_filtered[col_name]):
                if filter_key.startswith("min_"):
                    df_filtered = df_filtered[df_filtered[col_name] >= val]
                elif filter_key.startswith("max_"):
                    df_filtered = df_filtered[df_filtered[col_name] <= val]

    # ============ Time Filters ============ #
    # We already created columns: CookTimeMinutes, PrepTimeMinutes, TotalTimeMinutes
    time_filters = [
        ("min_cook_time", "CookTimeMinutes"),
        ("max_cook_time", "CookTimeMinutes"),
        ("min_prep_time", "PrepTimeMinutes"),
        ("max_prep_time", "PrepTimeMinutes"),
        ("min_total_time", "TotalTimeMinutes"),
        ("max_total_time", "TotalTimeMinutes"),
    ]

    for filter_key, col_name in time_filters:
        if filter_key in filters and col_name in df_filtered.columns:
            val = filters[filter_key]
            if filter_key.startswith("min_"):
                df_filtered = df_filtered[df_filtered[col_name] >= val]
            elif filter_key.startswith("max_"):
                df_filtered = df_filtered[df_filtered[col_name] <= val]

    # ============ Date Filters ============ #
    # If your 'DatePublished' column is a datetime, you can do range filtering
    if "date_published_before" in filters and "DatePublished" in df_filtered.columns:
        try:
            cutoff = pd.to_datetime(filters["date_published_before"])
            df_filtered = df_filtered[df_filtered["DatePublished"] <= cutoff]
        except:
            pass

    if "date_published_after" in filters and "DatePublished" in df_filtered.columns:
        try:
            cutoff = pd.to_datetime(filters["date_published_after"])
            df_filtered = df_filtered[df_filtered["DatePublished"] >= cutoff]
        except:
            pass

    # ============ String / Partial Match Filters ============ #
    # e.g., name_contains, author_name_contains, description_contains
    if "name_contains" in filters and "Name" in df_filtered.columns:
        search_terms = filters["name_contains"]
        df_filtered = df_filtered[
            df_filtered["Name"].apply(lambda x: str_contains_any(x, search_terms))
        ]

    if "name_exact" in filters and "Name" in df_filtered.columns:
        exact_term = filters["name_exact"].strip().lower()
        df_filtered = df_filtered[
            df_filtered["Name"].str.lower().eq(exact_term)
        ]

    if "author_name_contains" in filters and "AuthorName" in df_filtered.columns:
        search_terms = filters["author_name_contains"]
        df_filtered = df_filtered[
            df_filtered["AuthorName"].apply(lambda x: str_contains_any(x, search_terms))
        ]

    if "description_contains" in filters and "Description" in df_filtered.columns:
        search_terms = filters["description_contains"]
        df_filtered = df_filtered[
            df_filtered["Description"].apply(lambda x: str_contains_any(x, search_terms))
        ]

    if "recipe_category_contains" in filters and "RecipeCategory" in df_filtered.columns:
        search_terms = filters["recipe_category_contains"]
        df_filtered = df_filtered[
            df_filtered["RecipeCategory"].apply(lambda x: str_contains_any(x, search_terms))
        ]

    # ============ Keywords Filters ============ #
    if "keywords_any" in filters and "Keywords" in df_filtered.columns:
        terms = [t.lower() for t in filters["keywords_any"]]
        df_filtered = df_filtered[
            df_filtered["Keywords"].apply(
                lambda kws: kws is not None and any(
                    t in (kw.lower() for kw in kws if kw is not None) for t in terms
                )
            )
        ]


    if "keywords_all" in filters and "Keywords" in df_filtered.columns:
        terms = [t.lower() for t in filters["keywords_all"]]
        df_filtered = df_filtered[
            df_filtered["Keywords"].apply(
                lambda kws: kws is not None and all(
                    t in (kw.lower() for kw in kws if kw is not None) for t in terms
                )
            )
        ]


    # ============ Required Ingredients ============ #
    if "required_ingredients" in filters and "RecipeIngredientParts" in df_filtered.columns:
        required = [r.lower() for r in filters["required_ingredients"]]
        # Simple approach: check if row's ingredient list includes each required ingredient
        df_filtered = df_filtered[
            df_filtered["RecipeIngredientParts"].apply(
                lambda ing_list: all(
                    any(r in ingredient.lower() for ingredient in ing_list)
                    for r in required
                )
            )
        ]

    # 4. Sort the DataFrame (example: by rating desc, then review count desc)
    #    Adjust to your preference or skip sorting entirely
    if "AggregatedRating" in df_filtered.columns and "ReviewCount" in df_filtered.columns:
        df_filtered = df_filtered.sort_values(
            by=["AggregatedRating", "ReviewCount"],
            ascending=[False, False]
        )

    # 5. Return top N results
    #    Adjust columns to whatever is needed in the final result
    return df_filtered.head(max_results)


# # # Suppose the user says:
# df = pd.read_csv("./dataset/dataset.csv")
# import openai
# client = openai.OpenAI(api_key="YOUR KEY")

from .prompt import generate_filters
# user_query = "Any strawberry recipes with calories greater than 200"
# df_results = advanced_recipe_search(user_query, 5, df)
# print(df_results)