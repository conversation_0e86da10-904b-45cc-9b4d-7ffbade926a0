
import pandas as pd
from langgraph.graph import StateGraph, END
from typing import TypedDict, List, Optional
from dateutil import parser

import openai
import os
from dotenv import load_dotenv

load_dotenv()

openai_api_key= os.getenv("API_KEY")

# Initialize clients and models
client = openai.OpenAI(api_key=openai_api_key)

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import date

class RecipeQueryFilters(BaseModel):
    # Numeric Filters
    min_calories: Optional[int] = None
    max_calories: Optional[int] = None
    min_rating: Optional[float] = None
    max_rating: Optional[float] = None
    min_review_count: Optional[int] = None
    max_review_count: Optional[int] = None
    min_fat_content: Optional[float] = None
    max_fat_content: Optional[float] = None
    min_saturated_fat_content: Optional[float] = None
    max_saturated_fat_content: Optional[float] = None
    min_cholesterol_content: Optional[float] = None
    max_cholesterol_content: Optional[float] = None
    min_sodium_content: Optional[float] = None
    max_sodium_content: Optional[float] = None
    min_carbohydrate_content: Optional[float] = None
    max_carbohydrate_content: Optional[float] = None
    min_fiber_content: Optional[float] = None
    max_fiber_content: Optional[float] = None
    min_sugar_content: Optional[float] = None
    max_sugar_content: Optional[float] = None
    min_protein_content: Optional[float] = None
    max_protein_content: Optional[float] = None

    # Time Filters
    max_cook_time: Optional[int] = None
    min_cook_time: Optional[int] = None
    max_prep_time: Optional[int] = None
    min_prep_time: Optional[int] = None
    max_total_time: Optional[int] = None
    min_total_time: Optional[int] = None

    # Date Filters
    date_published_before: Optional[date] = None
    date_published_after: Optional[date] = None

    # String-based Filters
    name_contains: Optional[List[str]] = None
    name_exact: Optional[str] = None
    author_name_contains: Optional[List[str]] = None
    description_contains: Optional[List[str]] = None
    recipe_category_contains: Optional[List[str]] = None

    # Keywords Filters
    keywords_any: Optional[List[str]] = None
    keywords_all: Optional[List[str]] = None

    # Ingredients Filters
    required_ingredients: Optional[List[str]] = None


import json

def generate_filters(client, context):
    system_prompt = f"""\
You are given a user query about recipes.
Parse the query and return a JSON with any relevant filters.

Possible keys to include (only if user’s query implies them):
- min_calories (integer)
- max_calories (integer)
- min_rating (float)
- max_rating (float)
- min_review_count (integer)
- max_review_count (integer)
- min_fat_content (float)
- max_fat_content (float)
- min_saturated_fat_content (float)
- max_saturated_fat_content (float)
- min_cholesterol_content (float)
- max_cholesterol_content (float)
- min_sodium_content (float)
- max_sodium_content (float)
- min_carbohydrate_content (float)
- max_carbohydrate_content (float)
- min_fiber_content (float)
- max_fiber_content (float)
- min_sugar_content (float)
- max_sugar_content (float)
- min_protein_content (float)
- max_protein_content (float)

# Time filters:
- max_cook_time (minutes as integer)
- min_cook_time (minutes as integer)
- max_prep_time (minutes as integer)
- min_prep_time (minutes as integer)
- max_total_time (minutes as integer)
- min_total_time (minutes as integer)

# Date filters:
- date_published_before (YYYY-MM-DD)
- date_published_after (YYYY-MM-DD)

# String-based filters:
- name_contains (array of strings)
- name_exact (string)
- author_name_contains (array of strings)
- description_contains (array of strings)
- recipe_category_contains (array of strings)

# Keywords filters:
- keywords_any (array of strings)
- keywords_all (array of strings)

# Ingredients filters:
- required_ingredients (array of strings)

Return ONLY valid JSON with the filters that apply.
Do NOT add extra keys.
Here is the information: {context}
"""

    system_prompt = system_prompt.format(context=context)

    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "user", "content": system_prompt}
        ],
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "RecipeQueryFilters",
                    "description": "filters generated based on user query",
                    "parameters": RecipeQueryFilters.model_json_schema(),
                },
            }
        ],
        tool_choice={
            "type": "function",
            "function": {"name": "RecipeQueryFilters"},
        },
        temperature=0.1,
    )

    job_description_json = completion.choices[0].message.tool_calls[0].function.arguments
    return json.loads(job_description_json)
