import os
from langgraph.graph import StateGraph, END
from langchain_core.prompts import ChatPromptTemplate
from typing import TypedDict, List, Literal, Optional
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from langchain_openai import OpenAIEmbeddings
import pandas as pd
import json
from openai import OpenAI
from langgraph.checkpoint.mongodb import MongoDBSaver
import os
from dotenv import load_dotenv
from pymongo import MongoClient
# from custom_saver import MongoDBSaver
import requests
import openai
import os
from dotenv import load_dotenv

load_dotenv()

openai_api_key= os.getenv("API_KEY")
mongodb_uri = os.getenv("MONGODB_URI")
groq_api_key = os.getenv("GROQ_API_KEY")

# Initialize MongoDB client and checkpointer
mongodb_client = MongoClient(mongodb_uri)
checkpointer = MongoDBSaver(mongodb_client)
client = openai.OpenAI(api_key=openai_api_key)


# # Suppose the user says:
df = pd.read_csv("./dataset/dataset.csv")
from .rag import advanced_recipe_search
from typing import Annotated

from typing_extensions import TypedDict

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages


# Initialize clients and models
client = OpenAI(api_key=openai_api_key)
embeddings = OpenAIEmbeddings(model="text-embedding-3-small", api_key=openai_api_key)

# Define state schema
class GraphState(TypedDict):
    query: str
    classification: Optional[Literal["related", "unrelated", "followup", "pleasentries"]]
    retrieved_data: Optional[List[dict]]
    top_context: Optional[List[str]]
    # response: List
    image: Optional[str]
    response: Annotated[list, add_messages]


def classify_query(state: GraphState):
    messages = [{
        "role": "system",
        "content": """Analyze both the text query and any attached image. Follow these rules:
        
        1. If an image is present:
           a. If it's NOT related to cooking/food (e.g., animals, landscapes, selfies), respond 'unrelated'
           b. If it is cooking-related, proceed to text classification
        
        2. For text classification:
           "related" → The query is directly about cooking, including recipes, ingredients, kitchen tools, techniques, or other food-related topics. Or the query is asking about some image description.
            "unrelated" → The query is about a completely different topic, not related to cooking. 
            "followup" → The query is a follow-up question related to a previous cooking discussion. This includes clarifications, elaborations, or extensions of a cooking-related topic.
            "pleasantries" → The query consists of greetings, small talk, or polite conversation (e.g., "hi," "how are you?").
            Respond strictly with one of these four labels: "related", "unrelated", "followup", or "pleasantries". No additional text.
            **IMPORTANT** Unrelated is only used when the user is asking about some  other topic, otherwise use the other categories strictly.
        
        **IMPORTANT**
        If you can't determine the classification of the text, or if the query is vague, then classify it as "related".
        Only label unrelated if the query is asking something on a different domain or subject. Don't classify vague queries like "Yes please", "What do you think" etc etc as unrelated, these are all also related.
        Respond with ONLY one label: unrelated, related, followup, or pleasantries."""
    }]

    content = [{"type": "text", "text": state["query"]}]
    
    if state.get("image"):
        content.append({
            "type": "image_url",
            "image_url": {"url": f"{state['image']}"}
        })

    messages.append({"role": "user", "content": content})

    from groq import Groq
    client = Groq(api_key=groq_api_key)
    
    response = client.chat.completions.create(
        # model="gpt-4o",
        model="meta-llama/llama-4-scout-17b-16e-instruct",
        messages=messages,
        temperature=0.5,
    )

    classification = response.choices[0].message.content.strip().lower()
    print("Classification is : ", classification)

    return {"classification": classification}


def retrieve_initial_data(state: GraphState):
    # Call advanced_recipe_search implementation
    if state['classification'].lower() == 'related':

        print("\nRetreiving data for query : ", state["query"])
        df_results = advanced_recipe_search(state["query"], 20, df)
        df_results.to_csv("./dataset/results.csv")

        print("Number of rows retrieved using RAG are ", df_results.shape[0])

        return {"retrieved_data": df_results.to_dict(orient='records')}
        # return {"retrieved_data": df_results[['Name', 'Description']].to_dict(orient='records')}
    
    return {"retrieved_data": None}

def semantic_ranking(state: GraphState):
    query = state["query"]
    recipes = state["retrieved_data"]
    recipe_embeds = None
    # print(state['retrieved_data'])

    if state['retrieved_data'] is not None and len(state['retrieved_data']) >=1:
        # Generate embeddings
        query_embed = embeddings.embed_query(query)
        recipe_texts = [
            f"{r.get('Name', '')} - {r.get('Description', '')} "
            f"{' '.join(r.get('RecipeIngredientParts', []))}"
            for r in recipes
        ]
        recipe_embeds = embeddings.embed_documents(recipe_texts)

    if recipe_embeds is None:
        return {"retrieved_data": "None", "top_context": "None"}

    # print(recipe_embeds)
    # Calculate similarities
    similarities = cosine_similarity([query_embed], recipe_embeds)[0]
    scored_recipes = sorted(zip(recipes, similarities), key=lambda x: x[1], reverse=True)

    # Get top 5 recipes and their context
    top_recipes = [r[0] for r in scored_recipes[:5]]
    context = [
        f"Recipe: {r.get('Name', '')}\n"
        # f"Description: {r.get('Description', '')}\n"
        f"RecipeInstructions: {r.get('RecipeInstructions', '')}\n"
        f"Ingredients: {', '.join(r.get('RecipeIngredientParts', []))}"
        for r in top_recipes
    ]

    top_recipe_names = [item['Name'] for item in top_recipes]

    print("Top recipe names after similarity search are ", top_recipe_names, "\n")
    return {"retrieved_data": top_recipes, "top_context": context}

def filter_messages(messages):

    if len(messages) > 3:
        messages = messages[-3:]  # Keep only the latest 5 messages
    return messages  # Return all messages if 5 or fewer

# from langchain_core.runnables import RunnableConfig
# def generate_response(state: GraphState, config: RunnableConfig):
#     context = "\n\n".join(state.get("top_context", [])) or "No relevant recipes found"
    
#     if state.get('response'):
#         state['response'] = filter_messages(state['response'])
#         history = f"Additional message history of the user:\n{state['response']}\n\n"
#     else:
#         history = ""

#     preferences = config['configurable']['preferences']
#     # print("gg", preferences)
#     # print("\n\n\n\n")
#     # print(history)
#     if state['image']:
#         # ADD IN IMAGE TOO

#     response = client.chat.completions.create(
#         model="gpt-4o",
#         messages=[
#             {
#                 "role": "system",
#                 "content": f"""You're a cooking assistant. Use this context and additional history to generate an answer to the user's question. Make sure to scan both context and additional history, as the answer to the question might be in any of them :
#                 {context} 

#                 Additional user preferences are: {preferences}. You can use these when generating a response, only when you have relevant context available.

#                 {history}**IMPORTANT INSTRUCTIONS**\n\n 1. If the context is unavailable, then you can politely say that you are not aware of the answer etc. NEVER provide information outside of the context at all. \n 2. Use the preferences only when the context is available, since if there is no context, then you should just politely say you are not aware."""
#             }, 
#             {
#                 "role": "user",
#                 "content": state["query"]
#             }
#         ],
#         temperature=0.7,
#     )

#     # print(response.choices[0].message.content)
#     return {"response": response.choices[0].message.content}


from langchain_core.runnables import RunnableConfig

def generate_response(state: GraphState, config: RunnableConfig):
    context = "\n\n".join(state.get("top_context", [])) or "No relevant recipes found"
    
    if state.get('response'):
        state['response'] = filter_messages(state['response'])
        history = f"Additional message history:\n{state['response']}\n\n"
    else:
        history = ""

    preferences = config['configurable']['preferences']
    system_message = ""
    user_content = state["query"]

    if state['image']:
        # Prompt with image handling
        system_message = f"""You're a cooking assistant that can analyze both text and images. Use these elements to answer:
        1. TEXT CONTEXT: {context}
        2. IMAGE: Analyze the provided image for visual elements related to cooking, including:
           - Ingredients shown
           - Cooking techniques/steps
           - Recipe presentation
           - Equipment used
        3. USER HISTORY: {history}
        4. PREFERENCES: {preferences} (only use if relevant)

        **RULES**
        - Cross-reference image content with text context when possible
        - If image shows recipe steps, describe them clearly
        - If image contains ingredients, list them with text context
        - If image and text conflict, prioritize text context
        - NEVER invent details not supported by image/text"""
        
        user_content = [
            {"type": "text", "text": state["query"]},
            {"type": "image_url", "image_url": {"url": state['image']}}
        ]
    else:
        # Text-only prompt
        system_message = f"""You're a cooking assistant. Use this context and additional history to generate an answer to the user's question. Make sure to scan both context and additional history, as the answer to the question might be in any of them :
        {context} 

        Additional user preferences are: {preferences}. You can use these when generating a response, only when you have relevant context available.

        {history}**IMPORTANT INSTRUCTIONS**\n\n 1. If the context or history is unavailable, then you can politely say that you are not aware of the answer etc. NEVER provide information outside of the context and history. \n 2. Use the preferences only when the context or history is available, since if there is no context or history, then you should just politely say you are not aware. Respond naturally as a normal human would"""

    from groq import Groq
    client = Groq(api_key=groq_api_key)

    
    response = client.chat.completions.create(
        # model="gpt-4o",
        model="meta-llama/llama-4-scout-17b-16e-instruct",
        # model="gemini-2.0-flash",
        messages=[
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_content}
        ],
        temperature=0.7,
    )
    return {"response": response.choices[0].message.content}


def unrelated_response(state: GraphState):
        return {"response": "Please ask questions related to cooking."}

# Build workflow
workflow = StateGraph(GraphState)

# Add nodes
workflow.add_node("classify", classify_query)
workflow.add_node("retrieve_initial", retrieve_initial_data)
workflow.add_node("rank_context", semantic_ranking)
workflow.add_node("generate_answer", generate_response)
workflow.add_node("unrelated", unrelated_response)

# Set entry point
workflow.set_entry_point("classify")

# Add conditional routing
def route_classification(state: GraphState):
    return "retrieve_initial" if state["classification"] in ["related", "followup", "pleasantries"] else "unrelated"

workflow.add_conditional_edges(
    "classify",
    route_classification,
    {"retrieve_initial": "retrieve_initial", "unrelated": "unrelated"}
)

# Add linear edges
workflow.add_edge("retrieve_initial", "rank_context")
workflow.add_edge("rank_context", "generate_answer")
workflow.add_edge("generate_answer", END)
workflow.add_edge("unrelated", END)

# Compile the graph
app = workflow.compile(checkpointer=checkpointer)


# from IPython.display import Image
# from langchain_core.runnables.graph import MermaidDrawMethod

# # Generate the image
# image_data = app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)

# # Save the image
# with open("graph.png", "wb") as f:
#     f.write(image_data)

# print("Image saved as graph.png")


# Example usage
# def run_query(query: str):
#     state = {"query": query}
#     res=app.invoke(state)
#     return res
