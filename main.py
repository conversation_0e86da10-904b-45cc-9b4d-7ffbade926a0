
# # To run the server: uvicorn filename:app --reload
from fastapi import Fast<PERSON><PERSON>
from pydantic import BaseModel
from uuid import uuid4
from app.graph import app as graph_app  # Import the compiled graph
import logging

from typing import Optional
logger = logging.getLogger(__name__)

app = FastAPI()

def format_preferences(prefs: dict) -> str:
    """Convert nested preferences dict into a human-readable string"""
    sections = []
    
    # Basic Preferences Section
    if 'basicPreference' in prefs:
        basic = prefs['basicPreference']
        section = [
            "Basic Preferences:",
            f"- Favorite Cuisine: {basic.get('favoriteCuisine', 'Not specified')}",
            f"- Dietary Restriction: {basic.get('dietaryRestriction', 'None')}",
            f"- Cooking Skill Level: {basic.get('skillLevel', 'Not specified')}"
        ]
        sections.append("\n".join(section))
    
    # Health Preferences Section
    if 'healthPreference' in prefs:
        health = prefs['healthPreference']
        health_info = []
        
        # Format health conditions (only include true values)
        conditions = [k for k, v in health.get('conditions', {}).items() if v]
        if conditions:
            health_info.append(f"- Managing: {', '.join(conditions)}")
        
        # Format allergies (only include true values)
        allergies = [k for k, v in health.get('allergies', {}).items() if v]
        if allergies:
            health_info.append(f"- Allergies: {', '.join(allergies)}")
        
        if health_info:
            sections.append("Health Considerations:\n" + "\n".join(health_info))
    
    # Lifestyle Preferences Section
    if 'lifestylePreference' in prefs:
        lifestyle = prefs['lifestylePreference']
        section = [
            "Lifestyle Preferences:",
            f"- Preferred Meal: {lifestyle.get('mealType', 'Not specified')}",
            f"- Portion Size: {lifestyle.get('portionSize', 'Not specified')}",
            f"- Main Appliance: {lifestyle.get('cookingAppliances', 'Not specified')}"
        ]
        sections.append("\n".join(section))
    
    return "\n\n".join(sections)

class QueryRequest(BaseModel):
    query: str
    image: Optional[str] = None  # Base64 encoded image
    thread_id: str = None
    preferences: dict

@app.post("/query")
def query_graph(data: QueryRequest):
    thread_id = data.thread_id or str(uuid4())
    
    state = {
        "query": data.query,
        "image": data.image
    }
    
    preferences_str = format_preferences(data.preferences)
    
    response = graph_app.invoke(
        state,
        {"configurable": {"thread_id": thread_id, "preferences": preferences_str}}
    )
    
    return {
        "response": response['response'][-1],
        "thread_id": thread_id
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001) 

